# Archivos de sistema y editores
.DS_Store
Thumbs.db
.idea/
.vscode/
.nbproject/

# Dependencias de Node.js
node_modules/
.grunt
Gruntfile.js
package.json

# Archivos de entorno y configuración local
.env
.env.*
app/etc/env.php
app/etc/config.php

# Magento: Caché, logs y archivos generados
var/
!var/.htaccess
generated/
!generated/.htaccess
pub/static/
!pub/static/.htaccess
pub/media/catalog/product/cache/
pub/media/tmp/
pub/media/theme_customization/
pub/media/sitemap/
pub/media/custom_options/
pub/media/import/
pub/media/captcha/
pub/media/favicon/
pub/media/logo/
pub/media/customer/
pub/media/attribute/
pub/media/analytics/
pub/media/downloadable/
pub/media/wysiwyg/
!pub/media/wysiwyg/.htaccess

# Magento: Archivos innecesarios del código fuente
/.buildpath
/.cache
/.metadata
/.project
/.settings
/.php_cs
/.php_cs.cache
/grunt-config.json

# Magento: Archivos del sistema
atlassian*
robots.txt
pub/robots.txt
sitemap.xml
pub/sitemap.xml

# Librerías de Magento
vendor/
!vendor/.htaccess
app/code/Magento/TestModule*

# Flex uploader (legacy Magento)
lib/internal/flex/uploader/
lib/internal/flex/varien/

# Otros
/deploy/
dist/
target/
TEST*.xml