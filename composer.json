{"name": "magento/project-community-edition", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "php-http/discovery": true, "cweagans/composer-patches": true}, "preferred-install": "dist", "sort-packages": true}, "version": "2.4.7-p4", "require": {"catgento/module-admin-activity": "^1.1", "code4business/freeproduct2": "^1.3", "experius/module-wysiwygdownloads": "^1.2", "magefan/module-facebook-pixel": "^2.7", "magefan/module-google-tag-manager": "^2.7", "magento/composer-dependency-version-audit-plugin": "~0.1", "magento/composer-root-update-plugin": "^2.0.4", "magento/product-community-edition": "2.4.7-p4", "magento/quality-patches": "^1.1", "magepow/infinitescroll": "^1.0", "mageworx/module-searchsuiteautocomplete": "^2.2", "mercadopago/adb-payment": "1.11.0"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "require-dev": {"allure-framework/allure-phpunit": "^2", "cweagans/composer-patches": "^1.7", "dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.22", "lusitanian/oauth": "^0.8", "magento/magento-coding-standard": "*", "magento/magento2-functional-testing-framework": "^4.7", "pdepend/pdepend": "^2.10", "phpmd/phpmd": "^2.12", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "sebastian/phpcpd": "^6.0", "symfony/finder": "^6.4", "vpietri/adm-quickdevbar": "^0.3.4"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://repo.magento.com/"}], "scripts": {"post-install-cmd": ["cweagans\\composer\\patches::postInstall"], "post-update-cmd": ["cweagans\\composer\\patches::postUpdate"]}, "extra": {"magento-force": "override", "patches": {"magento/module-checkout": {"Fix checkout loading issue": "patches/fix-checkout-loading-issue.patch"}}}}