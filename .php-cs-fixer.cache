{"php": "8.3.24", "version": "3.71.0:v3.71.0#3825ffdc69501e1c9230291b79f036a0c0d8749d", "indent": "    ", "lineEnding": "\n", "rules": {"blank_line_after_namespace": true, "braces_position": true, "class_definition": true, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"attribute_placement": "ignore", "on_multiline": "ensure_fully_multiline"}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": {"elements": ["property"]}, "single_import_per_statement": true, "single_line_after_imports": true, "single_space_around_construct": {"constructs_followed_by_a_single_space": ["abstract", "as", "case", "catch", "class", "do", "else", "elseif", "final", "for", "foreach", "function", "if", "interface", "namespace", "private", "protected", "public", "static", "switch", "trait", "try", "use_lambda", "while"], "constructs_preceded_by_a_single_space": ["as", "else", "elseif", "use_lambda"]}, "spaces_inside_parentheses": true, "statement_indentation": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "visibility_required": {"elements": ["method", "property"]}, "encoding": true, "full_opening_tag": true, "array_syntax": {"syntax": "short"}, "concat_space": {"spacing": "one"}, "include": true, "new_with_braces": true, "no_empty_statement": true, "no_extra_blank_lines": true, "no_leading_import_slash": true, "no_leading_namespace_whitespace": true, "no_multiline_whitespace_around_double_arrow": true, "multiline_whitespace_before_semicolons": true, "no_singleline_whitespace_before_semicolons": true, "no_trailing_comma_in_singleline_array": true, "no_unused_imports": true, "no_whitespace_in_blank_line": true, "object_operator_without_whitespace": true, "ordered_imports": true, "standardize_not_equals": true, "ternary_operator_spaces": true}, "hashes": {"/tmp/PHP CS Fixertemp_folder/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder1/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder2/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder10/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder7/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder8/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder6/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder4/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder72/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder3/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder600/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder9/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder294/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder441/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder321/app/etc/config.php": "********************************", "/tmp/PHP CS Fixertemp_folder214/app/etc/env.php": "********************************", "/tmp/PHP CS Fixertemp_folder371/app/code/Profar/CheckoutFields/Plugin/Checkout/LayoutProcessorPlugin.php": "e87af4671d2b7b4f87a0dc5d1f20f756", "/tmp/PHP CS Fixertemp_folder5/app/code/Profar/CartRecipes/Plugin/OrderRepositoryPlugin.php": "cad232b3d7ffee0ab845c3a4245b891c", "/tmp/PHP CS Fixertemp_folder683/app/etc/config.php": "********************************", "/tmp/PHP CS Fixertemp_folder211/app/etc/config.php": "********************************", "/tmp/PHP CS Fixertemp_folder11/app/etc/config.php": "********************************", "/tmp/PHP CS Fixertemp_folder314/app/etc/config.php": "********************************", "/tmp/PHP CS Fixertemp_folder607/app/etc/config.php": "********************************", "/tmp/PHP CS Fixertemp_folder68/app/etc/config.php": "********************************"}}