<?php
/**
 * Script para Limpieza de Datos de Suscripciones
 * ParadoxLabs Subscriptions Module
 * 
 * Uso: php bin/magento dev:console < cleanup_subscriptions.php
 * 
 * @author: Limpieza completa del módulo ParadoxLabs Subscriptions
 */

use Magento\Framework\App\Bootstrap;
use Magento\Framework\Exception\LocalizedException;

require_once 'app/bootstrap.php';

class SubscriptionCleaner
{
    /**
     * @var \Magento\Framework\ObjectManagerInterface
     */
    private $objectManager;

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $resourceConnection;

    /**
     * @var \Magento\Framework\DB\Adapter\AdapterInterface
     */
    private $connection;

    public function __construct()
    {
        $bootstrap = Bootstrap::create(BP, $_SERVER);
        $this->objectManager = $bootstrap->getObjectManager();
        
        // Establecer el área para evitar errores
        $state = $this->objectManager->get(\Magento\Framework\App\State::class);
        try {
            $state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            // Ya está establecido
        }
        
        $this->resourceConnection = $this->objectManager->get(\Magento\Framework\App\ResourceConnection::class);
        $this->connection = $this->resourceConnection->getConnection();
    }

    /**
     * Limpiar todas las suscripciones y datos relacionados
     */
    public function cleanupAll($includeProductOptions = true, $includeEavAttributes = true)
    {
        echo "=== INICIANDO LIMPIEZA DE SUSCRIPCIONES ===\n\n";
        
        $stats = [
            'subscriptions' => 0,
            'intervals' => 0,
            'product_options' => 0,
            'eav_attributes' => 0
        ];

        try {
            // 1. Limpiar suscripciones activas
            $stats['subscriptions'] = $this->cleanupSubscriptions();
            
            // 2. Limpiar intervalos de productos
            $stats['intervals'] = $this->cleanupProductIntervals();
            
            // 3. Limpiar opciones personalizadas de productos (opcional)
            if ($includeProductOptions) {
                $stats['product_options'] = $this->cleanupProductOptions();
            }
            
            // 4. Limpiar atributos EAV de productos (opcional)
            if ($includeEavAttributes) {
                $stats['eav_attributes'] = $this->cleanupEavAttributes();
            }
            
            echo "\n=== RESUMEN DE LIMPIEZA ===\n";
            echo "Suscripciones eliminadas: {$stats['subscriptions']}\n";
            echo "Intervalos eliminados: {$stats['intervals']}\n";
            echo "Opciones de producto eliminadas: {$stats['product_options']}\n";
            echo "Atributos EAV limpiados: {$stats['eav_attributes']}\n";
            echo "\n✅ LIMPIEZA COMPLETADA\n";
            
            return $stats;
            
        } catch (\Exception $e) {
            echo "❌ ERROR: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * Limpiar suscripciones de clientes
     */
    private function cleanupSubscriptions()
    {
        echo "🧹 Limpiando suscripciones activas...\n";
        
        $tables = [
            'paradoxlabs_subscription',
            'paradoxlabs_subscription_log'
        ];
        
        $totalDeleted = 0;
        
        foreach ($tables as $tableName) {
            $table = $this->resourceConnection->getTableName($tableName);
            
            if ($this->tableExists($table)) {
                $count = $this->connection->fetchOne("SELECT COUNT(*) FROM {$table}");
                echo "  - Tabla {$tableName}: {$count} registros\n";
                
                $this->connection->delete($table);
                $totalDeleted += $count;
                echo "    ✓ Eliminados\n";
            } else {
                echo "  - Tabla {$tableName}: No existe\n";
            }
        }
        
        return $totalDeleted;
    }

    /**
     * Limpiar intervalos de productos
     */
    private function cleanupProductIntervals()
    {
        echo "\n🧹 Limpiando intervalos de productos...\n";
        
        $tableName = 'paradoxlabs_subscription_product_interval';
        $table = $this->resourceConnection->getTableName($tableName);
        
        if ($this->tableExists($table)) {
            $count = $this->connection->fetchOne("SELECT COUNT(*) FROM {$table}");
            echo "  - Tabla {$tableName}: {$count} registros\n";
            
            $this->connection->delete($table);
            echo "    ✓ Eliminados\n";
            return $count;
        } else {
            echo "  - Tabla {$tableName}: No existe\n";
            return 0;
        }
    }

    /**
     * Limpiar opciones personalizadas de suscripción
     */
    private function cleanupProductOptions()
    {
        echo "\n🧹 Limpiando opciones personalizadas de suscripción...\n";
        
        // Buscar opciones que contengan "suscri" en el título
        $sql = "
            SELECT DISTINCT cpo.option_id, cpo.product_id, cpot.title
            FROM catalog_product_option cpo
            JOIN catalog_product_option_title cpot ON cpo.option_id = cpot.option_id
            WHERE cpot.title LIKE '%suscri%' 
               OR cpot.title LIKE '%subscription%'
               OR cpo.type = 'drop_down'
        ";
        
        $subscriptionOptions = $this->connection->fetchAll($sql);
        
        echo "  - Opciones de suscripción encontradas: " . count($subscriptionOptions) . "\n";
        
        $deletedOptions = 0;
        foreach ($subscriptionOptions as $option) {
            echo "    - Producto {$option['product_id']}: '{$option['title']}'\n";
            
            // Primero obtener los option_type_ids para esta opción
            $optionTypeIds = $this->connection->fetchCol("
                SELECT option_type_id 
                FROM catalog_product_option_type_value 
                WHERE option_id = ?
            ", [$option['option_id']]);
            
            // Eliminar títulos de valores de opción
            if (!empty($optionTypeIds)) {
                $this->connection->delete(
                    $this->resourceConnection->getTableName('catalog_product_option_type_title'),
                    ['option_type_id IN (?)' => $optionTypeIds]
                );
            }
            
            // Eliminar valores de opción
            $this->connection->delete(
                $this->resourceConnection->getTableName('catalog_product_option_type_value'),
                ['option_id = ?' => $option['option_id']]
            );
            
            // Eliminar títulos de opción
            $this->connection->delete(
                $this->resourceConnection->getTableName('catalog_product_option_title'),
                ['option_id = ?' => $option['option_id']]
            );
            
            // Eliminar la opción
            $this->connection->delete(
                $this->resourceConnection->getTableName('catalog_product_option'),
                ['option_id = ?' => $option['option_id']]
            );
            
            $deletedOptions++;
        }
        
        echo "    ✓ {$deletedOptions} opciones eliminadas\n";
        return $deletedOptions;
    }

    /**
     * Limpiar atributos EAV de suscripción
     */
    private function cleanupEavAttributes()
    {
        echo "\n🧹 Limpiando atributos EAV de suscripción...\n";
        
        $subscriptionAttributes = [
            'subscription_active',
            'subscription_allow_onetime',
            'subscription_intervals',
            'subscription_unit',
            'subscription_length',
            'subscription_price',
            'subscription_init_adjustment'
        ];
        
        $productEntityTable = $this->resourceConnection->getTableName('catalog_product_entity');
        $eavAttributeTable = $this->resourceConnection->getTableName('eav_attribute');
        
        $totalCleaned = 0;
        
        foreach ($subscriptionAttributes as $attributeCode) {
            // Obtener attribute_id
            $attributeId = $this->connection->fetchOne("
                SELECT attribute_id 
                FROM {$eavAttributeTable} 
                WHERE attribute_code = ? AND entity_type_id = (
                    SELECT entity_type_id FROM eav_entity_type WHERE entity_type_code = 'catalog_product'
                )
            ", [$attributeCode]);
            
            if ($attributeId) {
                // Limpiar valores en tablas EAV
                $tables = [
                    'catalog_product_entity_int',
                    'catalog_product_entity_varchar',
                    'catalog_product_entity_text',
                    'catalog_product_entity_decimal'
                ];
                
                $attributeCleaned = 0;
                foreach ($tables as $tableName) {
                    $table = $this->resourceConnection->getTableName($tableName);
                    $count = $this->connection->fetchOne("
                        SELECT COUNT(*) FROM {$table} WHERE attribute_id = ?
                    ", [$attributeId]);
                    
                    if ($count > 0) {
                        $this->connection->delete($table, ['attribute_id = ?' => $attributeId]);
                        $attributeCleaned += $count;
                    }
                }
                
                echo "  - {$attributeCode}: {$attributeCleaned} valores limpiados\n";
                $totalCleaned += $attributeCleaned;
            } else {
                echo "  - {$attributeCode}: No encontrado\n";
            }
        }
        
        return $totalCleaned;
    }

    /**
     * Verificar si una tabla existe
     */
    private function tableExists($tableName)
    {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->connection->fetchOne($sql, [$tableName]);
        return !empty($result);
    }

    /**
     * Limpiar solo productos específicos
     */
    public function cleanupSpecificProducts(array $productIds)
    {
        echo "=== LIMPIEZA DE PRODUCTOS ESPECÍFICOS ===\n";
        echo "Productos a limpiar: " . implode(', ', $productIds) . "\n\n";
        
        $stats = [
            'intervals' => 0,
            'options' => 0,
            'attributes' => 0
        ];
        
        // Limpiar intervalos
        $intervalTable = $this->resourceConnection->getTableName('paradoxlabs_subscription_product_interval');
        if ($this->tableExists($intervalTable)) {
            $stats['intervals'] = $this->connection->delete($intervalTable, [
                'product_id IN (?)' => $productIds
            ]);
            echo "Intervalos eliminados: {$stats['intervals']}\n";
        }
        
        // Limpiar opciones personalizadas
        foreach ($productIds as $productId) {
            $options = $this->connection->fetchAll("
                SELECT option_id FROM catalog_product_option WHERE product_id = ?
            ", [$productId]);
            
            foreach ($options as $option) {
                $this->connection->delete(
                    $this->resourceConnection->getTableName('catalog_product_option_type_value'),
                    ['option_id = ?' => $option['option_id']]
                );
                $this->connection->delete(
                    $this->resourceConnection->getTableName('catalog_product_option_title'),
                    ['option_id = ?' => $option['option_id']]
                );
                $this->connection->delete(
                    $this->resourceConnection->getTableName('catalog_product_option'),
                    ['option_id = ?' => $option['option_id']]
                );
                $stats['options']++;
            }
        }
        
        echo "Opciones eliminadas: {$stats['options']}\n";
        echo "✅ Limpieza específica completada\n";
        
        return $stats;
    }
}

// ===============================
// EJEMPLOS DE USO
// ===============================

try {
    $cleaner = new SubscriptionCleaner();

    echo "Seleccione el tipo de limpieza:\n";
    echo "1. Limpieza completa (todo)\n";
    echo "2. Solo suscripciones e intervalos (mantener opciones)\n";
    echo "3. Solo producto específico\n";
    echo "4. Vista previa (sin eliminar)\n";
    
    // Para uso automático, descomente la opción deseada:
    
    // OPCIÓN 1: Limpieza completa
    $results = $cleaner->cleanupAll(true, true);
    
    // OPCIÓN 2: Solo datos de suscripción (mantener opciones)
    // $results = $cleaner->cleanupAll(false, false);
    
    // OPCIÓN 3: Solo productos específicos
    // $results = $cleaner->cleanupSpecificProducts([5829]); // ID del producto 109567
    
} catch (\Exception $e) {
    echo "Error fatal: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "PROCESO DE LIMPIEZA COMPLETADO\n";
echo str_repeat("=", 60) . "\n";
