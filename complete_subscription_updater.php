<?php
/**
 * Script COMPLETO para Suscripciones con Opciones Personalizadas
 * Crea todo lo necesario para que aparezcan en el frontend
 */

use Magento\Framework\App\Bootstrap;
use Magento\Framework\App\State;

require_once 'app/bootstrap.php';

class CompleteSubscriptionUpdater 
{
    private $objectManager;
    private $appState;
    private $resource;
    private $connection;

    public function __construct()
    {
        $bootstrap = Bootstrap::create(BP, $_SERVER);
        $this->objectManager = $bootstrap->getObjectManager();
        $this->appState = $this->objectManager->get(State::class);
        $this->appState->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        
        $this->resource = $this->objectManager->get(\Magento\Framework\App\ResourceConnection::class);
        $this->connection = $this->resource->getConnection();
    }

    public function updateSKU($sku, $intervals = '14,21,28,30,35,60,90', $verbose = true)
    {
        try {
            if ($verbose) {
                echo "🎯 CONFIGURACIÓN COMPLETA DE SUSCRIPCIÓN\n";
                echo "========================================\n";
                echo "SKU: {$sku}\n";
                echo "Intervalos: {$intervals}\n\n";
            }

            // 1. Obtener product_id del SKU
            $productId = $this->getProductIdBySKU($sku);
            if (!$productId) {
                if ($verbose) echo "❌ SKU no encontrado\n";
                return false;
            }
            
            if ($verbose) echo "✓ Producto ID: {$productId}\n";

            // 2. Actualizar atributos EAV directamente
            $this->updateEAVAttributes($productId, $intervals, $verbose);

            // 3. Crear opción personalizada y sus valores
            $optionId = $this->createCustomOption($productId, $verbose);

            // 4. Crear intervalos vinculados a la opción
            $this->createLinkedIntervals($productId, $optionId, $intervals, $verbose);

            // 5. Verificar
            if ($verbose) $this->verify($productId);

            return true;

        } catch (\Exception $e) {
            if ($verbose) {
                echo "❌ Error: " . $e->getMessage() . "\n";
                echo "Stack trace: " . $e->getTraceAsString() . "\n";
            }
            return false;
        }
    }

    private function getProductIdBySKU($sku)
    {
        $productTable = $this->resource->getTableName('catalog_product_entity');
        return $this->connection->fetchOne(
            "SELECT entity_id FROM {$productTable} WHERE sku = ?",
            [$sku]
        );
    }

    private function updateEAVAttributes($productId, $intervals, $verbose = true)
    {
        if ($verbose) echo "📝 Actualizando atributos EAV...\n";
        
        $attributes = [
            'subscription_active' => 1,
            'subscription_allow_onetime' => 1,
            'subscription_intervals' => $intervals,
            'subscription_unit' => 'day',
            'subscription_length' => 0
        ];

        $eavTable = $this->resource->getTableName('eav_attribute');
        $intTable = $this->resource->getTableName('catalog_product_entity_int');
        $varcharTable = $this->resource->getTableName('catalog_product_entity_varchar');

        foreach ($attributes as $attributeCode => $value) {
            $attributeId = $this->connection->fetchOne(
                "SELECT attribute_id FROM {$eavTable} WHERE attribute_code = ?",
                [$attributeCode]
            );

            if (!$attributeId) {
                if ($verbose) echo "  ⚠️ Atributo {$attributeCode} no encontrado\n";
                continue;
            }

            $table = is_numeric($value) ? $intTable : $varcharTable;
            
            $this->connection->insertOnDuplicate($table, [
                'attribute_id' => $attributeId,
                'store_id' => 0,
                'entity_id' => $productId,
                'value' => $value
            ], ['value']);

            if ($verbose) echo "  ✓ {$attributeCode}: {$value}\n";
        }
    }

    private function createCustomOption($productId, $verbose = true)
    {
        if ($verbose) echo "\n🎛️ Creando opción personalizada...\n";
        
        $optionTable = $this->resource->getTableName('catalog_product_option');
        $titleTable = $this->resource->getTableName('catalog_product_option_title');
        
        // Limpiar opciones existentes de suscripción
        $existingOptions = $this->connection->fetchCol(
            "SELECT co.option_id FROM {$optionTable} co 
             LEFT JOIN {$titleTable} cot ON co.option_id = cot.option_id 
             WHERE co.product_id = ? AND (cot.title LIKE '%suscrib%' OR cot.title LIKE '%Suscrib%')",
            [$productId]
        );
        
        foreach ($existingOptions as $optionId) {
            $this->deleteCustomOption($optionId);
            if ($verbose) echo "  🗑️ Opción anterior eliminada: {$optionId}\n";
        }
        
        // Crear nueva opción
        $optionData = [
            'product_id' => $productId,
            'type' => 'drop_down',
            'is_require' => 0,
            'sku' => null,
            'max_characters' => null,
            'file_extension' => null,
            'image_size_x' => null,
            'image_size_y' => null,
            'sort_order' => 1
        ];
        
        $this->connection->insert($optionTable, $optionData);
        $optionId = $this->connection->lastInsertId();
        
        // Crear título de opción
        $titleData = [
            'option_id' => $optionId,
            'store_id' => 0,
            'title' => 'Suscribirse a este producto'
        ];
        
        $this->connection->insert($titleTable, $titleData);
        
        if ($verbose) echo "  ✅ Opción creada: ID {$optionId}\n";
        return $optionId;
    }

    private function createLinkedIntervals($productId, $optionId, $intervalString, $verbose = true)
    {
        if ($verbose) echo "\n🔗 Creando valores de opción e intervalos vinculados...\n";
        
        $intervals = explode(',', $intervalString);
        
        $valueTable = $this->resource->getTableName('catalog_product_option_type_value');
        $valueTitleTable = $this->resource->getTableName('catalog_product_option_type_title');
        $intervalTable = $this->resource->getTableName('paradoxlabs_subscription_product_interval');
        
        // Limpiar intervalos existentes
        $deleted = $this->connection->delete($intervalTable, ['product_id = ?' => $productId]);
        if ($verbose) echo "  🗑️ Intervalos anteriores eliminados: {$deleted}\n";
        
        foreach ($intervals as $index => $interval) {
            $interval = trim($interval);
            if (empty($interval)) continue;
            
            // Crear valor de opción
            $valueData = [
                'option_id' => $optionId,
                'sku' => null,
                'sort_order' => ((int)$interval) + 1
            ];
            
            $this->connection->insert($valueTable, $valueData);
            $valueId = $this->connection->lastInsertId();
            
            // Crear título del valor
            $valueTitleData = [
                'option_type_id' => $valueId,
                'store_id' => 0,
                'title' => "Cada {$interval} días"
            ];
            
            $this->connection->insert($valueTitleTable, $valueTitleData);
            
            // Crear intervalo vinculado
            $intervalData = [
                'product_id' => $productId,
                'option_id' => $optionId,
                'value_id' => $valueId,
                'store_id' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'frequency_count' => (int)$interval,
                'frequency_unit' => 'day',
                'length' => null,
                'installment_price' => null,
                'adjustment_price' => null,
                'additional_information' => null
            ];
            
            $this->connection->insert($intervalTable, $intervalData);
            if ($verbose) echo "  ✅ {$interval} días → Option: {$optionId}, Value: {$valueId}\n";
        }
    }

    private function deleteCustomOption($optionId)
    {
        // Primero obtener todos los option_type_id asociados a este option_id
        $valueTable = $this->resource->getTableName('catalog_product_option_type_value');
        $optionTypeIds = $this->connection->fetchCol(
            "SELECT option_type_id FROM {$valueTable} WHERE option_id = ?",
            [$optionId]
        );
        
        // Eliminar títulos de valores de opción
        if (!empty($optionTypeIds)) {
            $titleTable = $this->resource->getTableName('catalog_product_option_type_title');
            $this->connection->delete($titleTable, ['option_type_id IN (?)' => $optionTypeIds]);
        }
        
        // Eliminar valores de opción
        $this->connection->delete($valueTable, ['option_id = ?' => $optionId]);
        
        // Eliminar título de opción
        $optionTitleTable = $this->resource->getTableName('catalog_product_option_title');
        $this->connection->delete($optionTitleTable, ['option_id = ?' => $optionId]);
        
        // Eliminar opción
        $optionTable = $this->resource->getTableName('catalog_product_option');
        $this->connection->delete($optionTable, ['option_id = ?' => $optionId]);
    }

    private function verify($productId)
    {
        echo "\n🔍 VERIFICACIÓN FINAL\n";
        echo "--------------------\n";
        
        // Verificar opción personalizada
        $optionTable = $this->resource->getTableName('catalog_product_option');
        $titleTable = $this->resource->getTableName('catalog_product_option_title');
        
        $option = $this->connection->fetchRow(
            "SELECT co.option_id, co.type, cot.title 
             FROM {$optionTable} co 
             LEFT JOIN {$titleTable} cot ON co.option_id = cot.option_id 
             WHERE co.product_id = ? AND cot.store_id = 0",
            [$productId]
        );
        
        if ($option) {
            echo "📋 Opción: '{$option['title']}' (ID: {$option['option_id']}, Tipo: {$option['type']})\n";
            
            // Verificar valores de opción
            $valueTable = $this->resource->getTableName('catalog_product_option_type_value');
            $valueTitleTable = $this->resource->getTableName('catalog_product_option_type_title');
            
            $values = $this->connection->fetchAll(
                "SELECT otv.option_type_id, ott.title 
                 FROM {$valueTable} otv 
                 LEFT JOIN {$valueTitleTable} ott ON otv.option_type_id = ott.option_type_id 
                 WHERE otv.option_id = ? AND ott.store_id = 0 
                 ORDER BY otv.sort_order",
                [$option['option_id']]
            );
            
            echo "🎯 Valores de opción: " . count($values) . "\n";
            foreach ($values as $value) {
                echo "  • {$value['title']} (ID: {$value['option_type_id']})\n";
            }
        }
        
        // Verificar intervalos
        $intervalTable = $this->resource->getTableName('paradoxlabs_subscription_product_interval');
        $intervals = $this->connection->fetchAll(
            "SELECT * FROM {$intervalTable} WHERE product_id = ? ORDER BY frequency_count",
            [$productId]
        );
        
        echo "\n📊 Intervalos en BD: " . count($intervals) . "\n";
        foreach ($intervals as $interval) {
            echo "  • {$interval['frequency_count']} {$interval['frequency_unit']} → Option: {$interval['option_id']}, Value: {$interval['value_id']}\n";
        }
        
        if (count($intervals) > 0 && $option) {
            echo "\n🎉 ¡CONFIGURACIÓN COMPLETA! Debería aparecer en el frontend\n";
        } else {
            echo "\n⚠️ Configuración incompleta\n";
        }
    }

    public function updateMultiple($skuList, $intervals = '14,21,28,30,35,60,90')
    {
        echo "🚀 ACTUALIZACIÓN MASIVA COMPLETA\n";
        echo "=================================\n";
        echo "📋 Total SKUs a procesar: " . count($skuList) . "\n\n";
        
        $results = [];
        $success = 0;
        $errors = 0;
        
        foreach ($skuList as $index => $sku) {
            $current = $index + 1;
            echo "➡️ [{$current}/" . count($skuList) . "] {$sku}: ";
            
            if ($this->updateSKU($sku, $intervals, false)) { // false = no verbose
                echo "✅ ÉXITO\n";
                $results[$sku] = true;
                $success++;
            } else {
                echo "❌ ERROR\n";
                $results[$sku] = false;
                $errors++;
            }
            
            // Mostrar progreso cada 10 productos
            if ($current % 10 == 0) {
                echo "\n📊 Progreso: {$current}/" . count($skuList) . " - Éxitos: {$success}, Errores: {$errors}\n";
                echo str_repeat("-", 50) . "\n\n";
            }
        }
        
        echo "\n🎯 RESUMEN FINAL:\n";
        echo "✅ Éxitos: {$success}\n";
        echo "❌ Errores: {$errors}\n";
        echo "📊 Total: " . count($skuList) . "\n";
        
        return $results;
    }

    public function loadSKUsFromFile($filePath)
    {
        if (!file_exists($filePath)) {
            throw new \Exception("Archivo no encontrado: {$filePath}");
        }
        
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        
        $skus = [];
        foreach ($lines as $line) {
            $sku = trim($line);
            // Saltar líneas vacías y la primera línea que dice "sku"
            if (!empty($sku) && $sku !== 'sku' && is_numeric($sku)) {
                $skus[] = $sku;
            }
        }
        
        return $skus;
    }
}

// EJECUCIÓN
try {
    $updater = new CompleteSubscriptionUpdater();
    
    $sku = isset($argv[1]) ? $argv[1] : '101963';
    $intervals = isset($argv[2]) ? $argv[2] : '14,21,28,30,35,60,90';
    
    if ($sku === 'MULTIPLE' || $sku === 'FILE') {
        // Cargar SKUs desde archivo
        $filePath = __DIR__ . '/subs_skus.txt';
        
        echo "📂 Cargando SKUs desde: {$filePath}\n";
        $skuList = $updater->loadSKUsFromFile($filePath);
        
        if (empty($skuList)) {
            echo "❌ No se encontraron SKUs válidos en el archivo\n";
            exit(1);
        }
        
        echo "✅ Cargados " . count($skuList) . " SKUs desde archivo\n\n";
        
        // Si se especifica un rango, procesarlo
        if (isset($argv[3]) && isset($argv[4])) {
            $start = (int)$argv[3] - 1; // Convertir a índice 0-based
            $end = (int)$argv[4] - 1;
            
            if ($start >= 0 && $end < count($skuList) && $start <= $end) {
                $skuList = array_slice($skuList, $start, $end - $start + 1);
                echo "🎯 Procesando rango: " . ($start + 1) . " a " . ($end + 1) . " (" . count($skuList) . " SKUs)\n\n";
            }
        }
        
        $updater->updateMultiple($skuList, $intervals);
    } else {
        $updater->updateSKU($sku, $intervals);
    }
    
} catch (\Exception $e) {
    echo "💥 Error: " . $e->getMessage() . "\n";
}

echo "\n🎯 USO:\n";
echo "  php complete_subscription_updater.php SKU \"14,21,28,30,35,60,90\"\n";
echo "  php complete_subscription_updater.php FILE \"14,21,28,30,35,60,90\"\n";
echo "  php complete_subscription_updater.php FILE \"14,21,28,30,35,60,90\" 1 50\n";
echo "  php complete_subscription_updater.php MULTIPLE \"14,21,28,30,35,60,90\"\n";
