image: php:8.1-cli

pipelines:
  custom:
    deploy-to-production:
      - step:
          name: Deploy to Production
          image: atlassian/default-image:2
          caches:
            - composer
          script:
            - echo "Starting manual deployment to Production Server"
            - echo "$BITBUCKET_BRANCH"
            # Actualización del sistema y herramientas necesarias
            - apt-get update && apt-get install -y ssh zip
            # Crear un archivo zip del repositorio (excluyendo var y vendor)
            - zip -r repo.zip . -x "var/*" -x "vendor/*"
            # Crear el directorio de despliegue en el servidor
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "mkdir -p $DEPLOY_PATH_PROD"
            # (Opcional) Verificar que el directorio se creó correctamente
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "ls -ld $DEPLOY_PATH_PROD"
            # Transferir el archivo zip al servidor
            - scp -rv repo.zip $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD:$DEPLOY_PATH_PROD/repo.zip
            # Respaldar archivos de configuración
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "
                mkdir -p $DEPLOY_PATH_PROD/config_pipeline &&
                cp $DEPLOY_PATH_PROD/app/etc/env.php $DEPLOY_PATH_PROD/config_pipeline/env.php &&
                cp $DEPLOY_PATH_PROD/app/etc/config.php $DEPLOY_PATH_PROD/config_pipeline/config.php
              "
            # Extraer el contenido del zip, sobrescribiendo archivos existentes
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "cd $DEPLOY_PATH_PROD && unzip -o repo.zip && rm repo.zip"
            # Restaurar la configuración respaldada
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "
                cp $DEPLOY_PATH_PROD/config_pipeline/env.php $DEPLOY_PATH_PROD/app/etc/env.php && 
                cp $DEPLOY_PATH_PROD/config_pipeline/config.php $DEPLOY_PATH_PROD/app/etc/config.php
              "
            # Eliminar el directorio vendor anterior, limpiar caché de Composer e instalar dependencias
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "cd $DEPLOY_PATH_PROD && rm -Rf vendor/"
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "cd $DEPLOY_PATH_PROD && composer clear-cache"
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "cd $DEPLOY_PATH_PROD && composer install"
            # Aplicar parches desde archivo
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "cd $DEPLOY_PATH_PROD && vendor/bin/magento-patches apply \$(cat quality-patches.txt) -n"
            # Ejecutar comandos de Magento (upgrade, compile, static content deploy, etc.)
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_PROD@$DEPLOY_HOST_PROD "
                cd $DEPLOY_PATH_PROD && 
                php bin/magento maintenance:enable;
                rm -rf generated/code/*;
                php bin/magento setup:upgrade; 
                php bin/magento setup:di:compile;
                php bin/magento indexer:reindex;
                php bin/magento setup:static-content:deploy -f;
                php bin/magento cache:flush;
                php bin/magento maintenance:disable
              "

    deploy-to-staging:
      - step:
          name: Deploy to Staging
          image: atlassian/default-image:2
          caches:
            - composer
          script:
            - echo "Starting manual deployment to Staging Server"
            - echo "$BITBUCKET_BRANCH"
            # Actualización del sistema y herramientas necesarias
            - apt-get update && apt-get install -y ssh zip
            # Crear un archivo zip del repositorio (excluyendo var y vendor)
            - zip -r repo.zip . -x "var/*" -x "vendor/*"
            # Crear el directorio de despliegue en el servidor staging
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "mkdir -p $DEPLOY_PATH_STG"
            # (Opcional) Verificar que el directorio se creó correctamente
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "ls -ld $DEPLOY_PATH_STG"
            # Transferir el archivo zip al servidor staging
            - scp -rv repo.zip $DEPLOY_USER_STG@$DEPLOY_HOST_STG:$DEPLOY_PATH_STG/repo.zip
            # Respaldar archivos de configuración
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "
                mkdir -p $DEPLOY_PATH_STG/config_pipeline && 
                cp $DEPLOY_PATH_STG/app/etc/env.php $DEPLOY_PATH_STG/config_pipeline/env.php && 
                cp $DEPLOY_PATH_STG/app/etc/config.php $DEPLOY_PATH_STG/config_pipeline/config.php
              "
            # Extraer el contenido del zip en staging
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "cd $DEPLOY_PATH_STG && unzip -o repo.zip && rm repo.zip"
            # Restaurar la configuración respaldada
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "
                cp $DEPLOY_PATH_STG/config_pipeline/env.php $DEPLOY_PATH_STG/app/etc/env.php && 
                cp $DEPLOY_PATH_STG/config_pipeline/config.php $DEPLOY_PATH_STG/app/etc/config.php
              "
            # Eliminar el directorio vendor anterior, limpiar caché de Composer e instalar dependencias
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "cd $DEPLOY_PATH_STG && rm -Rf vendor/"
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "cd $DEPLOY_PATH_STG && composer clear-cache"
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "cd $DEPLOY_PATH_STG && composer install"
            # Aplicar parches desde archivo
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "cd $DEPLOY_PATH_STG && vendor/bin/magento-patches apply \$(cat quality-patches.txt) -n"
            # Ejecutar comandos de Magento en stagng
            - ssh -o StrictHostKeyChecking=no $DEPLOY_USER_STG@$DEPLOY_HOST_STG "
                cd $DEPLOY_PATH_STG && 
                php bin/magento maintenance:enable && 
                rm -rf generated/code/* &&
                php bin/magento setup:upgrade && 
                php bin/magento setup:di:compile && 
                php bin/magento indexer:reindex &&
                php bin/magento setup:static-content:deploy -f && 
                php bin/magento cache:flush && 
                php bin/magento maintenance:disable
              "
