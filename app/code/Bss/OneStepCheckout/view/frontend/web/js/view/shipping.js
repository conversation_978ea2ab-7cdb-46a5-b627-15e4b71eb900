/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category  BSS
 * @package   Bss_OneStepCheckout
 * <AUTHOR> Team
 * @copyright Copyright (c) 2017-2018 BSS Commerce Co. ( http://bsscommerce.com )
 * @license   http://bsscommerce.com/Bss-Commerce-License.txt
 */

define([
    'jquery',
    'underscore',
    'Magento_Ui/js/form/form',
    'ko',
    'Magento_Customer/js/model/customer',
    'Magento_Customer/js/model/address-list',
    'Magento_Checkout/js/model/address-converter',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/action/create-shipping-address',
    'Magento_Checkout/js/action/select-shipping-address',
    'Bss_OneStepCheckout/js/model/shipping-rates-validator',
    'Magento_Checkout/js/model/shipping-address/form-popup-state',
    'Magento_Checkout/js/model/shipping-service',
    'Magento_Checkout/js/action/select-shipping-method',
    'Magento_Checkout/js/model/shipping-rate-registry',
    'Magento_Checkout/js/action/set-shipping-information',
    'Magento_Checkout/js/model/step-navigator',
    'Magento_Ui/js/modal/modal',
    'Magento_Checkout/js/model/checkout-data-resolver',
    'Magento_Checkout/js/checkout-data',
    'uiRegistry',
    'mage/translate',
    'Magento_Checkout/js/model/shipping-rate-service'
], function (
    $,
    _,
    Component,
    ko,
    customer,
    addressList,
    addressConverter,
    quote,
    createShippingAddress,
    selectShippingAddress,
    shippingRatesValidator,
    formPopUpState,
    shippingService,
    selectShippingMethodAction,
    rateRegistry,
    setShippingInformationAction,
    stepNavigator,
    modal,
    checkoutDataResolver,
    checkoutData,
    registry,
    $t
) {
    'use strict';

    var popUp = null;

    return Component.extend({
        defaults: {
            template: 'Magento_Checkout/shipping',
            shippingFormTemplate: 'Bss_OneStepCheckout/shipping-address/form',
            shippingMethodListTemplate: 'Magento_Checkout/shipping-address/shipping-method-list',
            shippingMethodItemTemplate: 'Magento_Checkout/shipping-address/shipping-method-item'
        },
        visible: ko.observable(!quote.isVirtual()),
        errorValidationMessage: ko.observable(false),
        isCustomerLoggedIn: customer.isLoggedIn,
        isFormPopUpVisible: formPopUpState.isVisible,
        isFormInline: addressList().length === 0,
        isNewAddressAdded: ko.observable(false),
        saveInAddressBook: 1,
        quoteIsVirtual: quote.isVirtual(),

        /**
         * @return {exports}
         */
        initialize: function () {
            var self = this,
                hasNewAddress,
                fieldsetName = 'checkout.steps.shipping-step.shippingAddress.shipping-address-fieldset';
            this._super();

            if (!quote.isVirtual()) {
                stepNavigator.registerStep(
                    'shipping',
                    '',
                    $t('Shipping'),
                    this.visible,
                    _.bind(this.navigate, this),
                    10
                );
            }
            checkoutDataResolver.resolveShippingAddress();

            hasNewAddress = addressList.some(function (address) {
                return address.getType() == 'new-customer-address'; //eslint-disable-line eqeqeq
            });

            this.isNewAddressAdded(hasNewAddress);

            this.isFormPopUpVisible.subscribe(function (value) {
                if (value) {
                    self.getPopUp().openModal();
                }
            });

            quote.shippingMethod.subscribe(function () {
                self.errorValidationMessage(false);
            });

            registry.async('checkoutProvider')(function (checkoutProvider) {
                var shippingAddressData = checkoutData.getShippingAddressFromData(),
                    addressesList = addressList();
                if (!checkoutData.getSelectedShippingAddress() &&
                    addressesList.length &&
                    undefined !== addressesList[0]) {
                    checkoutData.setSelectedShippingAddress(addressesList[0]);
                }
                if (shippingAddressData) {
                    checkoutProvider.set(
                        'shippingAddress',
                        $.extend(true, {}, checkoutProvider.get('shippingAddress'), shippingAddressData)
                    );
                }
                checkoutProvider.on('shippingAddress', function (shippingAddrsData) {
                    checkoutData.setShippingAddressFromData(shippingAddrsData);
                });
                shippingRatesValidator.initFields(fieldsetName);
            });

            quote.shippingAddress.subscribe(function (address) {
                let customAttributes = address.customAttributes;
                var documentType = "";
                var documentNumber = "";
                for (let i = 0; i < customAttributes.length; i++) {
                    if(customAttributes[i].attribute_code === "document_number" ){
                        documentNumber = customAttributes[i].value;
                        if(documentNumber === ''){
                            let documentField = $('[name="custom_attributes[document_number]"]');
                            let comuna_id = $('[name="comuna_id"]');
                            comuna_id.val('');
                            documentField.focus();
                        }
                    }
                    if(customAttributes[i].attribute_code === "document_type" ){
                        documentType = customAttributes[i].value;
                    }
                }
                if (documentType && documentNumber) {
                    var url = '/profar/quote/save';
                    var payload = {
                        document_type: documentType,
                        document_number: documentNumber,
                        is_customer: true
                    };

                    $.ajax({
                        url: url,
                        data: payload,
                        type: 'POST',
                        dataType: 'json'
                    }).fail(function (response) {
                    });
                }
            }, this);


            return this;
        },

        /**
         * Navigator change hash handler.
         *
         * @param {Object} step - navigation step
         */
        navigate: function (step) {
            step && step.isVisible(true);
        },

        /**
         * @return {*}
         */
        getPopUp: function () {
            var self = this,
                buttons;

            if (!popUp) {
                buttons = this.popUpForm.options.buttons;
                this.popUpForm.options.buttons = [
                    {
                        text: buttons.save.text ? buttons.save.text : $t('Save Address'),
                        class: buttons.save.class ? buttons.save.class : 'action primary action-save-address',
                        click: self.saveNewAddress.bind(self)
                    },
                    {
                        text: buttons.cancel.text ? buttons.cancel.text : $t('Cancel'),
                        class: buttons.cancel.class ? buttons.cancel.class : 'action secondary action-hide-popup',

                        /** @inheritdoc */
                        click: this.onClosePopUp.bind(this)
                    }
                ];

                /** @inheritdoc */
                this.popUpForm.options.closed = function () {
                    self.isFormPopUpVisible(false);
                };

                this.popUpForm.options.modalCloseBtnHandler = this.onClosePopUp.bind(this);
                this.popUpForm.options.keyEventHandlers = {
                    escapeKey: this.onClosePopUp.bind(this)
                };

                /** @inheritdoc */
                this.popUpForm.options.opened = function () {
                    // Store temporary address for revert action in case when user click cancel action
                    self.temporaryAddress = $.extend(true, {}, checkoutData.getShippingAddressFromData());
                };
                popUp = modal(this.popUpForm.options, $(this.popUpForm.element));
            }
            self.autoFillAddress(this.popUpForm.element);
            return popUp;
        },

        /**
         * Revert address and close modal.
         */
        onClosePopUp: function () {
            checkoutData.setShippingAddressFromData($.extend(true, {}, this.temporaryAddress));
            this.getPopUp().closeModal();
        },

        /**
         * Show address form popup
         */
        showFormPopUp: function () {
            this.isFormPopUpVisible(true);
        },

        /**
         * Save new shipping address
         */
        saveNewAddress: function () {
            var addressData,
                newShippingAddress;

            this.source.set('params.invalid', false);
            this.triggerShippingDataValidateEvent();

            if (!this.source.get('params.invalid')) {
                addressData = this.source.get('shippingAddress');
                // if user clicked the checkbox, its value is true or false. Need to convert.
                addressData['save_in_address_book'] = this.saveInAddressBook ? 1 : 0;

                // New address must be selected as a shipping address
                newShippingAddress = createShippingAddress(addressData);
                selectShippingAddress(newShippingAddress);
                checkoutData.setSelectedShippingAddress(newShippingAddress.getKey());
                checkoutData.setNewCustomerShippingAddress($.extend(true, {}, addressData));
                this.getPopUp().closeModal();
                this.isNewAddressAdded(true);
            }
        },

        /**
         * Shipping Method View
         */
        rates: shippingService.getShippingRates(),
        isLoading: shippingService.isLoading,
        isSelected: ko.computed(function () {
            return quote.shippingMethod() ?
                quote.shippingMethod()['carrier_code'] + '_' + quote.shippingMethod()['method_code'] :
                null;
        }),

        /**
         * @param {Object} shippingMethod
         * @return {Boolean}
         */
        selectShippingMethod: function (shippingMethod) {
            selectShippingMethodAction(shippingMethod);
            checkoutData.setSelectedShippingRate(shippingMethod['carrier_code'] + '_' + shippingMethod['method_code']);

            return true;
        },

        /**
         * Set shipping information handler
         */
        setShippingInformation: function () {
            if (this.validateShippingInformation()) {
                setShippingInformationAction().done(
                    function () {
                        stepNavigator.next();
                    }
                );
            }
        },

        /**
         * @return {Boolean}
         */
        validateShippingInformation: function () {
            var shippingAddress,
                addressData,
                loginFormSelector = 'form[data-role=email-with-possible-login]',
                emailValidationResult = customer.isLoggedIn(),
                field;

            if (!quote.shippingMethod()) {
                this.errorValidationMessage($t('Please specify a shipping method.'));

                return false;
            }

            if (!customer.isLoggedIn()) {
                $(loginFormSelector).validation();
                emailValidationResult = Boolean($(loginFormSelector + ' input[name=username]').valid());
            }

            if (this.isFormInline) {
                this.source.set('params.invalid', false);
                this.triggerShippingDataValidateEvent();

                if (emailValidationResult &&
                    this.source.get('params.invalid') ||
                    !quote.shippingMethod()['method_code'] ||
                    !quote.shippingMethod()['carrier_code']
                ) {
                    this.focusInvalid();

                    return false;
                }

                shippingAddress = quote.shippingAddress();
                addressData = addressConverter.formAddressDataToQuoteAddress(
                    this.source.get('shippingAddress')
                );

                //Copy form data to quote shipping address object
                for (field in addressData) {
                    if (addressData.hasOwnProperty(field) && //eslint-disable-line max-depth
                        shippingAddress.hasOwnProperty(field) &&
                        typeof addressData[field] != 'function' &&
                        _.isEqual(shippingAddress[field], addressData[field])
                    ) {
                        shippingAddress[field] = addressData[field];
                    } else if (typeof addressData[field] != 'function' &&
                        !_.isEqual(shippingAddress[field], addressData[field])) {
                        shippingAddress = addressData;
                        break;
                    }
                }

                if (customer.isLoggedIn()) {
                    shippingAddress['save_in_address_book'] = 1;
                }
                window.shippingAddress = shippingAddress;
                //selectShippingAddress(shippingAddress);
            }

            if (!emailValidationResult) {
                $(loginFormSelector + ' input[name=username]').focus();

                return false;
            }

            return true;
        },

        /**
         * Trigger Shipping data Validate Event.
         */
        triggerShippingDataValidateEvent: function () {
            this.source.trigger('shippingAddress.data.validate');

            if (this.source.get('shippingAddress.custom_attributes')) {
                this.source.trigger('shippingAddress.custom_attributes.data.validate');
            }
        },

        /**
         * Auto Fill Address
         * @param element
         */
        autoFillAddress: function (element) {
            var self = this;
            if (typeof(element)  === "object") {
                var formId = element.id;
            } else if (typeof(element)  === "string") {
                var formId = element.replace("#", "");
            }
            if (this.isFormInline || typeof(element) === "string") {
                setTimeout(function(){
                    var streetId = $('#' + formId + ' [name="street[0]"]').id;
                    var street = $('#' + formId + ' [name="street[0]"]').val();
                    if (street == '') {
                        if (typeof window.geoAddress !== "undefined" && window.geoAddress.length !== 0) {
                            self.fillInAddress(window.geoAddress, streetId);
                        } else {
                            self.fillCountry(formId);
                        }
                    }
                }, 500);
            }
        },

        /**
         * Fill Address
         * @param address
         * @param id
         */
        fillInAddress: function (address, id) {
            var componentFields = [
                'country_id',
                'postcode',
                'region_id',
                'region',
                'city',
                'street'
            ];
            var country = false,
                countryList = window.checkoutConfig.bssOsc.googleApiListCountries,
                useRegionId = false,
                countryElement = false,
                regionIdElement = false,
                component = 'checkout.steps.shipping-step.shippingAddress.shipping-address-fieldset';
            registry.get(component, function (formComponent) {
                $.each(componentFields, function (index, field) {
                    var element = formComponent.getChild(field);
                    if (field === 'region') {
                        element = formComponent.getChild('region_id_input');
                    }

                    if (field == 'country_id' && field in address) {
                        $('#' + element.uid).find('option').each(function () {
                            if ($(this).attr('value') == address[field]) {
                                var currentCountry = element.value();
                                element.value(address[field]);
                                country = address[field];
                                countryElement = element;
                                if (($.inArray(currentCountry, countryList) === -1 && $.inArray(address[field], countryList) !== -1) ||
                                    ($.inArray(currentCountry, countryList) !== -1 && $.inArray(address[field], countryList) !== -1 && currentCountry != address[field])
                                ) {
                                    element.trigger('change');
                                }
                                return false;
                            }
                        });
                    }
                    if (field == 'region_id' && field in address && country != false && $.inArray(country, countryList) !== -1) {
                        $('#' + element.uid).find('option').each(function () {
                            if ($(this).attr('data-title') == address[field]) {
                                element.value($(this).attr('value'));
                                regionIdElement = element;
                                return false;
                            }
                        });
                        useRegionId = true;
                    }
                    if (field == 'region' && country != false && useRegionId == false) {
                        if ('region_id' in address) {
                            element.value(address['region_id']);
                        } else {
                            element.value('');
                        }
                    }
                    if (field == 'street' && field in address) {
                        element = formComponent.getChild(field).getChild(0);
                        element.value(address[field]);
                    }
                    if ((field == 'postcode' || field == 'city')) {
                        if (field in address) {
                            element.value(address[field]);
                        } else {
                            element.value('');
                        }
                    }
                });
            });

            if (country != '' && component == shipping) {
                if (useRegionId == true && regionIdElement != false) {
                    regionIdElement.trigger('change');
                } else {
                    if (countryElement != false) {
                        countryElement.trigger('change');
                    }
                }
            }
        },

        /**
         * Fill Country
         * @param id
         * @param countryCode
         */
        fillCountry: function (formId) {
            var countryCode = window.checkoutConfig.bssOsc.googleApiCustomerCountry,
                countrySelector = $('#' + formId + ' [name="country_id"]'),
                currentCountry = countrySelector.val();
            if (currentCountry == countryCode) {
                return;
            }
            countrySelector.find('option').each(function () {
                if ($(this).attr('value') == countryCode) {
                    countrySelector.val(countryCode);
                    countrySelector.trigger('change');
                    return false;
                }
            });
        }
    });
});
