{"name": "knownonline/module-mercadopago-order", "description": "KnownOnline MercadoPago Order Management - Prevents premature order creation in MercadoPago checkout", "type": "magento2-module", "version": "1.0.0", "license": "proprietary", "authors": [{"name": "KnownOnline", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "magento/framework": "*", "magento/module-sales": "*", "magento/module-quote": "*", "magento/module-payment": "*"}, "autoload": {"files": ["registration.php"], "psr-4": {"KnownOnline\\MercadopagoOrder\\": ""}}}